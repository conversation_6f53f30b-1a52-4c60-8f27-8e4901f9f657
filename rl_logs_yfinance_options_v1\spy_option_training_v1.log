2025-09-12 10:07:08,323 - INFO - SPY.py:2108 - Logging configured for Option Trader v1.
2025-09-12 10:07:08,339 - INFO - SPY.py:2109 - Log file: c:\Users\<USER>\Desktop\Test 1 Remove close_IRX      (8 features)\rl_logs_yfinance_options_v1\spy_option_training_v1.log
2025-09-12 10:07:08,339 - INFO - SPY.py:2110 - Tickers to fetch: ['SPY', '^VIX', '^VIX3M', '^TNX']
2025-09-12 10:07:08,339 - INFO - SPY.py:2118 - YFinance session management disabled - letting yfinance handle its own sessions with curl_cffi.
2025-09-12 10:07:08,339 - INFO - SPY.py:5457 - --- Option Trader v3.1 ---
2025-09-12 10:07:08,339 - INFO - SPY.py:5559 - [INFO] Main: Preparing Data for Training
2025-09-12 10:07:08,339 - INFO - SPY.py:5572 - Fetching train data for SPY (elapsed: 0.0s)
2025-09-12 10:07:09,943 - INFO - SPY.py:2405 - Skipping additional feature calculations for SPY - using reduced feature set
2025-09-12 10:07:09,943 - INFO - SPY.py:2408 - SPY: Keeping columns ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY']
2025-09-12 10:07:09,943 - INFO - SPY.py:2436 - Processed SPY data shape: (501, 5). Columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY']
2025-09-12 10:07:09,943 - INFO - SPY.py:2437 - Data quality check: 0/2505 (0.0%) zero values
2025-09-12 10:07:09,943 - INFO - SPY.py:713 - [INFO] PerformanceMonitor: Operation fetch_refactored_SPY_1757642828 SUCCESS in 1.60s [Ticker: SPY] [Operation: refactored_fetch]
2025-09-12 10:07:09,943 - INFO - SPY.py:5609 - SUCCESS: Validated authentic training data for SPY: 501 rows
2025-09-12 10:07:11,953 - INFO - SPY.py:5572 - Fetching train data for ^VIX (elapsed: 3.6s)
2025-09-12 10:07:12,754 - INFO - SPY.py:1936 - Attempt 1/3 with timeout 20s
2025-09-12 10:07:13,004 - INFO - SPY.py:2281 - Processing ^VIX data with shape (501, 7)
2025-09-12 10:07:13,004 - INFO - SPY.py:2282 - Raw ^VIX columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']
2025-09-12 10:07:13,020 - INFO - SPY.py:2284 - Raw ^VIX Close values - first: 32.95000076293945, last: 11.9399995803833
2025-09-12 10:07:13,020 - INFO - SPY.py:2204 - Applying VIX validation for VIX
2025-09-12 10:07:13,020 - INFO - SPY.py:2363 - Processed VIX close values - first: 32.95000076293945, last: 11.9399995803833
2025-09-12 10:07:13,020 - INFO - SPY.py:2396 - Market index ^VIX: Keeping only ['close_VIX']
2025-09-12 10:07:13,020 - INFO - SPY.py:2436 - Processed ^VIX data shape: (501, 1). Columns: ['close_VIX']
2025-09-12 10:07:13,020 - INFO - SPY.py:2437 - Data quality check: 0/501 (0.0%) zero values
2025-09-12 10:07:13,020 - INFO - SPY.py:983 - VALIDATION: ^VIX last value: 11.9399995803833
2025-09-12 10:07:13,020 - INFO - SPY.py:713 - [INFO] PerformanceMonitor: Operation fetch_refactored_^VIX_1757642831 SUCCESS in 1.07s [Ticker: ^VIX] [Operation: refactored_fetch]
2025-09-12 10:07:13,020 - INFO - SPY.py:5609 - SUCCESS: Validated authentic training data for ^VIX: 501 rows
2025-09-12 10:07:15,024 - INFO - SPY.py:5572 - Fetching train data for ^VIX3M (elapsed: 6.7s)
2025-09-12 10:07:15,826 - INFO - SPY.py:1936 - Attempt 1/3 with timeout 25s
2025-09-12 10:07:16,029 - INFO - SPY.py:2396 - Market index ^VIX3M: Keeping only ['close_VIX3M']
2025-09-12 10:07:16,029 - INFO - SPY.py:2436 - Processed ^VIX3M data shape: (501, 1). Columns: ['close_VIX3M']
2025-09-12 10:07:16,029 - INFO - SPY.py:2437 - Data quality check: 0/501 (0.0%) zero values
2025-09-12 10:07:16,029 - INFO - SPY.py:983 - VALIDATION: ^VIX3M last value: 14.170000076293945
2025-09-12 10:07:16,029 - INFO - SPY.py:713 - [INFO] PerformanceMonitor: Operation fetch_refactored_^VIX3M_1757642835 SUCCESS in 1.01s [Ticker: ^VIX3M] [Operation: refactored_fetch]
2025-09-12 10:07:16,029 - INFO - SPY.py:5609 - SUCCESS: Validated authentic training data for ^VIX3M: 501 rows
2025-09-12 10:07:18,045 - INFO - SPY.py:5572 - Fetching train data for ^TNX (elapsed: 9.7s)
2025-09-12 10:07:18,847 - INFO - SPY.py:1936 - Attempt 1/3 with timeout 20s
2025-09-12 10:07:19,144 - INFO - SPY.py:2221 - Applying standardized Treasury rate validation for TNX (10-year Treasury yield)
2025-09-12 10:07:19,144 - INFO - SPY.py:2222 - ASSUMPTION: Input data is in percentage format, will convert to decimal format
2025-09-12 10:07:19,144 - INFO - SPY.py:2231 - TNX (10-year Treasury yield) converted from percentage to decimal format
2025-09-12 10:07:19,144 - INFO - SPY.py:2234 - TNX (10-year Treasury yield) after conversion to decimal: min=0.026060, max=0.049880, median=0.039220
2025-09-12 10:07:19,144 - INFO - SPY.py:2363 - Processed TNX (10-year Treasury yield) close values - first: 0.03306999921798706, last: 0.0423799991607666
2025-09-12 10:07:19,144 - INFO - SPY.py:2370 - VALIDATION: Final TNX (10-year Treasury yield) rate (decimal format): 0.042380 (4.2380%)
2025-09-12 10:07:19,144 - INFO - SPY.py:2396 - Market index ^TNX: Keeping only ['close_TNX']
2025-09-12 10:07:19,144 - INFO - SPY.py:2436 - Processed ^TNX data shape: (501, 1). Columns: ['close_TNX']
2025-09-12 10:07:19,144 - INFO - SPY.py:2437 - Data quality check: 0/501 (0.0%) zero values
2025-09-12 10:07:19,144 - INFO - SPY.py:983 - VALIDATION: ^TNX last value: 0.0423799991607666
2025-09-12 10:07:19,144 - INFO - SPY.py:713 - [INFO] PerformanceMonitor: Operation fetch_refactored_^TNX_1757642838 SUCCESS in 1.10s [Ticker: ^TNX] [Operation: refactored_fetch]
2025-09-12 10:07:19,144 - INFO - SPY.py:5609 - SUCCESS: Validated authentic training data for ^TNX: 501 rows
2025-09-12 10:07:19,144 - ERROR - SPY.py:5619 - CRITICAL TICKER MISSING: ^IRX not in training data or empty
2025-09-12 10:07:19,144 - CRITICAL - SPY.py:5620 - STOPPING SCRIPT: Critical ticker ^IRX missing from training data. Cannot proceed without complete authentic dataset.
2025-09-12 10:07:19,144 - INFO - SPY.py:8741 - [INFO] Main: Script execution completed
2025-09-12 10:07:19,144 - INFO - SPY.py:758 - [INFO] PerformanceMonitor: Performance Summary: 4/4 operations successful (100.0% success rate)
2025-09-12 10:07:19,144 - INFO - SPY.py:766 - [INFO] PerformanceMonitor: Average refactored_fetch time: 1.19s
